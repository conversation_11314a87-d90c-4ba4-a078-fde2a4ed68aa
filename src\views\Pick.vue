<template>
  <div class="pick-bg">
    <TopLogo
      firstSrc="/img/TopLogo0.png"
      secondSrc="/img/TopLogo1.png"
      height="85px"
      transitionDuration="800"
    />

    <div class="pick-title">Pick 你喜欢的公仔</div>
    <div
      class="container"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @mousedown="handleTouchStart"
      @mousemove="handleTouchMove"
      @mouseup="handleTouchEnd"
      @mouseleave="handleTouchEnd"
    >
      <div
        v-for="(card, idx) in cards"
        :key="card.name"
        class="card"
        :class="{
          active: idx === activeIndex,
          left: getOffset(idx) === -1,
          right: getOffset(idx) === 1,
          left2: getOffset(idx) === -2,
          right2: getOffset(idx) === 2,
        }"
        :style="{
          zIndex: 10 - Math.abs(getOffset(idx)),
        }"
        @click="handleCardClick(idx)"
      >
        <div class="img">
          <img :src="card.img" :alt="card.name" />
        </div>
      </div>
    </div>
    <div class="pick-btn-wrap">
      <button class="pink-btn" @click="chooseCard(cards[activeIndex])">
        就它了
      </button>
      
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
import { useRouter } from "vue-router";
import TopLogo from "@/components/TopLogo.vue";

// 路由实例（基于你的路由配置）
const router = useRouter();
const headerHeight = ref("75rem");

// 1. 卡片与路由映射表（关联你的路由路径）
const routeMap = {
  zzz: "/gym", // zzz 对应 /gym（RhythmicGymnastics.vue）
  // 后续可扩展其他卡片：
  // '笑笑虾': '/xxx',
  // '呆呆豚': '/yyy',
};

// 2. 公仔数据
const cards = [
  { name: "笑笑虾", img: "/img/xia.png", type: "tennis", bg: "/img/tennis-bg.png" },
  { name: "呆呆鱼", img: "/img/mgu.png", type: "tennis", bg: "/img/badminton-bg.png" },
  { name: "酷酷蟹", img: "/img/ip3.png", type: "trampoline", bg: "/img/trampoline-bg.png" },
  { name: "萌萌蛙", img: "/img/ip4.png", type: "trampoline", bg: "/img/gymnastics-bg.png" },
  { name: "憨憨贝", img: "/img/ip5.png", type: "archery", bg: "/img/archery-bg.png" },
  { name: "可可牛", img: "/img/tcchimiandf.png", type: "gymnastics", bg: "/img/swimming-bg.png" },
  { name: "zzz", img: "/img/11.png", bg: "/img/default-bg.png" },
  { name: "yyy", img: "/img/11.png", bg: "/img/default-bg.png" },
];

// 3. 边框颜色
const borderColors = [
  "#eeb2d2",
  "#f9d6b7",
  "#b7e3f9",
  "#f9f3b7",
  "#f9c7b7",
  "#d2b7f9",
  "#b7f9d6",
];

// 4. 状态管理
const activeIndex = ref(2);
const currentBg = ref(cards[2].bg || ''); // 当前背景图片
let timer = null;
let resumeTimer = null;
const isTransitioning = ref(false);
const touchStartX = ref(0);
const touchMoveX = ref(0);
const isDragging = ref(false);
const hasMoved = ref(false);

// 5. 监听activeIndex变化，更新背景
watch(activeIndex, (newIndex) => {
  currentBg.value = cards[newIndex].bg || '';
});

// 6. 计算卡片偏移量
function getOffset(idx) {
  const len = cards.length;
  let offset = idx - activeIndex.value;
  if (offset > len / 2) offset -= len;
  if (offset < -len / 2) offset += len;
  return offset;
}

// 7. 自动轮播逻辑
function startAutoRotate() {
  if (timer) return;
  timer = setInterval(() => {
    if (!isTransitioning.value) nextCard();
  }, 3000);
}

function stopAutoRotate() {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  if (resumeTimer) {
    clearTimeout(resumeTimer);
    resumeTimer = null;
  }
}

// 8. 卡片切换
function prevCard() {
  if (isTransitioning.value) return;
  isTransitioning.value = true;
  activeIndex.value = (activeIndex.value - 1 + cards.length) % cards.length;
  stopAutoRotate();
  setTimeout(() => {
    isTransitioning.value = false;
  }, 700);
}

function nextCard() {
  if (isTransitioning.value) return;
  isTransitioning.value = true;
  activeIndex.value = (activeIndex.value + 1) % cards.length;
  stopAutoRotate();
  setTimeout(() => {
    isTransitioning.value = false;
  }, 700);
}

// 9. 卡片点击处理
function handleCardClick(idx) {
  if (hasMoved.value) {
    hasMoved.value = false;
    return;
  }
  if (idx !== activeIndex.value) {
    activeIndex.value = idx;
    stopAutoRotate();
  } else {
    chooseCard(cards[idx]);
  }
}

// 10. 选择卡片（核心：路由跳转逻辑）
function chooseCard(card) {
  stopAutoRotate();
  const targetPath = routeMap[card.name];

  if (targetPath) {
    // 配置了路由：跳转对应页面（如zzz跳转到/gym）
    router.push(targetPath);
  } else {
    // 未配置路由：保留弹窗提示
    alert(`你选择了 ${card.name}，暂未配置游戏页面`);
  }
}

// 11. 滑动事件处理
function handleTouchStart(e) {
  touchStartX.value = e.type.includes("mouse")
    ? e.clientX
    : e.touches[0].clientX;
  isDragging.value = true;
  hasMoved.value = false;
  stopAutoRotate();
}

function handleTouchMove(e) {
  if (!isDragging.value) return;
  const currentX = e.type.includes("mouse") ? e.clientX : e.touches[0].clientX;
  if (Math.abs(currentX - touchStartX.value) > 5) {
    hasMoved.value = true;
  }
  touchMoveX.value = currentX;
}

function handleTouchEnd() {
  if (!isDragging.value) return;

  const diffX = touchMoveX.value - touchStartX.value;
  const threshold = 50;

  if (diffX > threshold) {
    prevCard();
  } else if (diffX < -threshold) {
    nextCard();
  }

  isDragging.value = false;
  setTimeout(() => {
    hasMoved.value = false;
  }, 100);
}

// 12. 生命周期
onMounted(() => {
  // 预加载所有背景图片
  cards.forEach(card => {
    if (card.bg) {
      const img = new Image();
      img.src = card.bg;
    }
  });

  startAutoRotate();
});

onUnmounted(() => {
  stopAutoRotate();
  isDragging.value = false;
  hasMoved.value = false;
});
</script>

<style scoped>
body {
  background: #f7f7fa;
}
.pick-bg {
  width: 100vw;
  height: 100vh;
  min-height: 100svh;
  min-width: 100svw;
  /* 基础渐变背景 */
  background: linear-gradient(180deg, #8ddfff 30%, #ffffff 70%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
  padding: 0;
  margin: 0;
  /* 安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 背景图片叠加层 */
.pick-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: v-bind(currentBg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.4; /* 调整透明度实现与原背景的混合效果 */
  transition: all 0.7s ease-in-out; /* 背景过渡动画 */
  z-index: 0; /* 在内容下方 */
  pointer-events: none; /* 不影响交互 */
}

.pick-title {
  font-size: 2.1rem;
  font-weight: bold;
  color: #2d3a4b;
  margin-top: 2.2rem;
  margin-bottom: 8.2rem;
  text-align: center;
  letter-spacing: 0.08em;
  text-shadow: 0 2px 8px #fff8;
  position: relative;
  z-index: 1; /* 确保在背景图片上方 */
}
.container {
  width: 100vw;
  height: 260px;
  max-width: 430px;
  min-height: 180px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  position: relative;
  margin-top: 3.5rem;
  margin-bottom: 6.6rem;
  perspective: 1200px;
  perspective-origin: 50% 60%;
  touch-action: pan-y;
  z-index: 1; /* 确保在背景图片上方 */
}

.card {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 170px;
  height: 220px;
  background: transparent;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.7) rotateY(0deg) translateZ(0px);
  transition: all 0.7s cubic-bezier(0.4, 1.2, 0.6, 1);
  z-index: 1;
  backface-visibility: hidden;
  will-change: transform, opacity;
  user-select: none;
  cursor: pointer;
  overflow: hidden;
  box-shadow: none;
}
.card .img {
  width: 100%;
  height: 100%;
  margin: 0;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  overflow: hidden;
}
.card .img img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 不裁切图片 */
  border-radius: 0;
}
.card-name {
  font-size: 1.2rem;
  color: #2d3a4b;
  font-weight: 600;
  margin-top: 2px;
  text-align: center;
  letter-spacing: 0.04em;
}
.card.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(2) rotateY(0deg) translateZ(220px);
  z-index: 5;
  box-shadow: none;
}
.card.left,
.card.right {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1) translateX(20vw) rotateY(0deg)
    translateZ(140px);
  z-index: 4;
  box-shadow: none;
}
.card.right {
  transform: translate(-50%, -50%) scale(1) translateX(-20vw) rotateY(0deg)
    translateZ(140px);
}
.card.left2 {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8) rotateY(0deg) translateZ(40px);
  z-index: 3;
}
.card.right2 {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8) translateX(20vw) rotateY(0deg)
    translateZ(40px);
  z-index: 3;
}
.pick-btn-wrap {
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2.2rem;
  position: relative;
  z-index: 1; /* 确保在背景图片上方 */
}
.pick-btn {
  background: none;
  border: none;
  outline: none;
  padding: 0;
  box-shadow: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 220px;
  height: 90px;
  background: transparent;
  cursor: pointer;
}

.pink-btn {
  background: #ff2985;
  border-radius: 50px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  padding: 1rem 2.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 70%;
  max-width: 220px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: 0 4px 15px rgba(255, 41, 133, 0.3);
  border: none;
}

/* .pink-btn:hover {
  background: #e0247a;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 41, 133, 0.4);
}

.pink-btn:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 10px rgba(255, 41, 133, 0.3);
} */
.pick-btn img {
  width: 70%;
  height: auto;
  display: block;
}

@media (max-width: 430px) {
  .container {
    height: 180px;
    min-height: 120px;
    margin-top: 0rem;
  }
  .pick-btn-wrap {
    margin-top: 2.2rem;
  }
  .pick-btn {
    width: 150px;
    height: 60px;
    margin-top: 6.2rem;
  }
  .card {
    width: 110px;
    height: 140px;
    border-radius: 0;
  }
  .card .img {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }
  .card .img img {
    border-radius: 0;
  }
  .card.active {
    transform: translate(-50%, -50%) scale(2) rotateY(0deg) translateZ(220px);
    box-shadow: none;
  }
  .card.left,
  .card.right {
    transform: translate(-50%, -35%) scale(1) translateX(30vw) rotateY(0deg)
      translateZ(140px);
    box-shadow: none;
  }
  .card.right {
    transform: translate(-50%, -35%) scale(1) translateX(-30vw) rotateY(0deg)
      translateZ(140px);
  }
}
</style>
