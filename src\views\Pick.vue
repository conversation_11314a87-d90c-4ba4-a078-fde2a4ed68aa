<template>
  <div class="pick-bg">
    <div class="app-header">
    <TopLogo
      firstSrc="/img/TopLogo0.png"
      secondSrc="/img/TopLogo1.png"
      height="75px"
      transitionDuration="800"
    />
  </div>
    <div class="pick-title">Pick 你喜欢的公仔</div>
    <div
      class="container"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @mousedown="handleTouchStart"
      @mousemove="handleTouchMove"
      @mouseup="handleTouchEnd"
      @mouseleave="handleTouchEnd"
    >
      <div
        v-for="(card, idx) in cards"
        :key="card.name"
        class="card"
        :class="{
          active: idx === activeIndex,
          left: getOffset(idx) === -1,
          right: getOffset(idx) === 1,
          left2: getOffset(idx) === -2,
          right2: getOffset(idx) === 2,
        }"
        :style="{
          borderColor: borderColors[idx % borderColors.length],
          zIndex: 10 - Math.abs(getOffset(idx)),
        }"
        @click="handleCardClick(idx)"
      >
        <div class="img">
          <img :src="card.img" :alt="card.name" />
        </div>
        <div class="card-name">{{ card.name }}</div>
      </div>
    </div>
    <div class="pick-btn-wrap">
      <button class="pick-btn" @click="chooseCard(cards[activeIndex])">
        <img src="/img/an1.png" alt="就它了" />
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import TopLogo from "@/components/TopLogo.vue";

// 路由实例（基于你的路由配置）
const router = useRouter();
const headerHeight = ref('75rem');

// 1. 卡片与路由映射表（关联你的路由路径）
const routeMap = {
  zzz: "/gym", // zzz 对应 /gym（RhythmicGymnastics.vue）
  // 后续可扩展其他卡片：
  // '笑笑虾': '/xxx',
  // '呆呆豚': '/yyy',
};

// 2. 公仔数据
const cards = [
  { name: "笑笑虾", img: "/img/ip1.png", type: "tennis" },
  { name: "呆呆鱼", img: "/img/ip2.png", type: "tennis" },
  { name: "酷酷蟹", img: "/img/ip3.png", type: "trampoline" },
  { name: "萌萌蛙", img: "/img/ip4.png", type: "trampoline" },
  { name: "憨憨贝", img: "/img/ip5.png", type: "archery" },
  { name: "可可牛", img: "/img/tcchimiandf.png", type: "gymnastics" },
  { name: "zzz", img: "/img/11.png" },
  { name: "yyy", img: "/img/11.png" },
];

// 3. 边框颜色
const borderColors = [
  "#eeb2d2",
  "#f9d6b7",
  "#b7e3f9",
  "#f9f3b7",
  "#f9c7b7",
  "#d2b7f9",
  "#b7f9d6",
];

// 4. 状态管理
const activeIndex = ref(2);
let timer = null;
let resumeTimer = null;
const isTransitioning = ref(false);
const touchStartX = ref(0);
const touchMoveX = ref(0);
const isDragging = ref(false);
const hasMoved = ref(false);

// 5. 计算卡片偏移量
function getOffset(idx) {
  const len = cards.length;
  let offset = idx - activeIndex.value;
  if (offset > len / 2) offset -= len;
  if (offset < -len / 2) offset += len;
  return offset;
}

// 6. 自动轮播逻辑
function startAutoRotate() {
  if (timer) return;
  timer = setInterval(() => {
    if (!isTransitioning.value) nextCard();
  }, 3000);
}

function stopAutoRotate() {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  if (resumeTimer) {
    clearTimeout(resumeTimer);
    resumeTimer = null;
  }
}

// 7. 卡片切换
function prevCard() {
  if (isTransitioning.value) return;
  isTransitioning.value = true;
  activeIndex.value = (activeIndex.value - 1 + cards.length) % cards.length;
  stopAutoRotate();
  setTimeout(() => {
    isTransitioning.value = false;
  }, 700);
}

function nextCard() {
  if (isTransitioning.value) return;
  isTransitioning.value = true;
  activeIndex.value = (activeIndex.value + 1) % cards.length;
  stopAutoRotate();
  setTimeout(() => {
    isTransitioning.value = false;
  }, 700);
}

// 8. 卡片点击处理
function handleCardClick(idx) {
  if (hasMoved.value) {
    hasMoved.value = false;
    return;
  }
  if (idx !== activeIndex.value) {
    activeIndex.value = idx;
    stopAutoRotate();
  } else {
    chooseCard(cards[idx]);
  }
}

// 9. 选择卡片（核心：路由跳转逻辑）
function chooseCard(card) {
  stopAutoRotate();
  const targetPath = routeMap[card.name];

  if (targetPath) {
    // 配置了路由：跳转对应页面（如zzz跳转到/gym）
    router.push(targetPath);
  } else {
    // 未配置路由：保留弹窗提示
    alert(`你选择了 ${card.name}，暂未配置游戏页面`);
  }
}

// 10. 滑动事件处理
function handleTouchStart(e) {
  touchStartX.value = e.type.includes("mouse")
    ? e.clientX
    : e.touches[0].clientX;
  isDragging.value = true;
  hasMoved.value = false;
  stopAutoRotate();
}

function handleTouchMove(e) {
  if (!isDragging.value) return;
  const currentX = e.type.includes("mouse") ? e.clientX : e.touches[0].clientX;
  if (Math.abs(currentX - touchStartX.value) > 5) {
    hasMoved.value = true;
  }
  touchMoveX.value = currentX;
}

function handleTouchEnd() {
  if (!isDragging.value) return;

  const diffX = touchMoveX.value - touchStartX.value;
  const threshold = 50;

  if (diffX > threshold) {
    prevCard();
  } else if (diffX < -threshold) {
    nextCard();
  }

  isDragging.value = false;
  setTimeout(() => {
    hasMoved.value = false;
  }, 100);
}

// 11. 生命周期
onMounted(() => {
  startAutoRotate();
});

onUnmounted(() => {
  stopAutoRotate();
  isDragging.value = false;
  hasMoved.value = false;
});
</script>

<style scoped>
body {
  background: #f7f7fa;
}
.pick-bg {
  width: 100vw;
  height: 100vh;
  min-height: 100svh;
  min-width: 100svw;
  background: linear-gradient(180deg, #8ddfff 30%, #ffffff 70%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
}
.app-header {
  position: fixed; /* 脱离文档流，固定在顶部 */
  top: 0;
  left: 0;
  width: 100%;
  /* height: v-bind(headerHeight); */
   margin: 0;
  padding: 0;
  
  overflow: hidden;
  z-index: 10; /* 确保在背景上方 */
  
}

.pick-title {
  font-size: 2.1rem;
  font-weight: bold;
  color: #2d3a4b;
  margin-top: 2.2rem;
  margin-bottom: 8.2rem;
  text-align: center;
  letter-spacing: 0.08em;
  text-shadow: 0 2px 8px #fff8;
}
.container {
  width: 100vw;
  height: 260px;
  max-width: 430px;
  min-height: 180px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  position: relative;
  margin-bottom: 6.6rem;
  perspective: 1200px;
  perspective-origin: 50% 60%;
  touch-action: pan-y;
}

.card {
  position: absolute;
  left: 50%;
  top: 54%;
  width: 170px;
  height: 220px;
  background: transparent;
  border: none;
  border-radius: 18px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.7) rotateY(0deg) translateZ(0px);
  transition: all 0.7s cubic-bezier(0.4, 1.2, 0.6, 1);
  z-index: 1;
  backface-visibility: hidden;
  will-change: transform, opacity;
  user-select: none;
  cursor: pointer;
}
.card .img {
  width: 130px;
  height: 130px;
  margin-top: 18px;
  margin-bottom: 8px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.07);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  overflow: hidden;
}
.card .img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 0;
}
.card-name {
  font-size: 1.2rem;
  color: #2d3a4b;
  font-weight: 600;
  margin-top: 2px;
  text-align: center;
  letter-spacing: 0.04em;
}
.card.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.18) rotateY(0deg) translateZ(220px);
  z-index: 5;
  box-shadow: 0 8px 32px rgba(80, 80, 180, 0.18), 0 2px 8px rgba(0, 0, 0, 0.1);
}
.card.left {
  opacity: 1;
  transform: translate(-50%, -50%) scale(0.95) translateX(22vw) rotateY(0deg)
    translateZ(140px);
  z-index: 4;
}
.card.right {
  opacity: 1;
  transform: translate(-50%, -50%) scale(0.95) translateX(-22vw) rotateY(0deg)
    translateZ(140px);
  z-index: 4;
}
.card.left2 {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8) rotateY(0deg) translateZ(40px);
  z-index: 3;
}
.card.right2 {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8) translateX(22vw) rotateY(0deg)
    translateZ(40px);
  z-index: 3;
}
.pick-btn-wrap {
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2.2rem;
}
.pick-btn {
  background: none;
  border: none;
  outline: none;
  padding: 0;
  box-shadow: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 220px;
  height: 90px;
  background: transparent;
  cursor: pointer;
}
.pick-btn img {
  width: 100%;
  height: auto;
  display: block;
}

@media (max-width: 430px) {
  .container {
    height: 180px;
    min-height: 120px;
  }
  .card {
    width: 110px;
    height: 140px;
  }
  .card .img {
    width: 70px;
    height: 70px;
  }
  .pick-btn {
    width: 150px;
    height: 60px;
    margin-top: 6.2rem;
  }
}
</style>
