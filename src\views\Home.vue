<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import AnimatedLogo from "../components/AnimatedLogo.vue";
const router = useRouter();
const showRules = ref(false);

function goToPick() {
  router.push("/pick");
}

function openRules() {
  showRules.value = true;
  // 显示模态框时禁止背景滚动
  document.body.style.overflow = "hidden";
}

function closeRules() {
  showRules.value = false;
  // 关闭模态框时恢复背景滚动
  document.body.style.overflow = "";
}

// 支持ESC键关闭模态框
function handleKeydown(e) {
  if (e.key === 'Escape' && showRules.value) {
    closeRules();
  }
}

onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
});
</script>

<template>
  <div class="container home-background">
    <header class="header">
      <img 
        alt="logo" 
        class="logo" 
        src="/img/logo.png" 
        onerror="this.src='/img/logo-fallback.png'"
      />
    </header>
    <main class="main">
      <AnimatedLogo />
    </main>
    <footer class="footer vertical">
      <button
        class="btn btn-img go"
        @click="goToPick"
        aria-label="GO按钮"
      ></button>
      <button
        class="btn btn-img rule"
        @click="openRules"
        aria-label="游戏规则"
      ></button>
    </footer>

    <!-- 游戏规则模态框 -->
    <div v-if="showRules" class="modal-overlay" @click="closeRules">
      <div 
        class="modal-content" 
        @click.stop 
        role="dialog" 
        aria-modal="true" 
        aria-labelledby="modal-title"
      >
        <div class="modal-header">
          <h2 id="modal-title">游戏规则</h2>
        </div>
        <div class="modal-body">
          <p>这里是游戏规则内容...</p>
          <p>1. 游戏开始后，玩家需要在限定时间内完成选择</p>
          <p>2. 每次选择将影响后续的游戏进程</p>
          <p>3. 完成所有关卡即可获得胜利</p>
          <p>4. 注意查看游戏提示，有助于顺利通关</p>
        </div>
        <!-- 底部返回按钮 -->
        <div class="modal-footer">
          <button class="back-btn" @click="closeRules">
            返回
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  font-family: "PingFang SC", "Helvetica Neue", Arial, "Hiragino Sans GB",
    "Microsoft YaHei", sans-serif;
  overflow: hidden;
  padding: 0;
  /* 安全区域适配 */
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.home-background {
  background: url("/img/gzaimian-bg1.png") no-repeat center center;
  background-size: cover;
  min-height: 100vh;
}
.header {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 4.5rem;
  margin-bottom: 0.5rem;
  padding: 0 1rem;
}
.logo {
  width: 100%;
  max-width: 200px;
  height: auto;
  display: block;
  object-fit: contain;
}
.title {
  font-size: 2.2rem;
  font-weight: bold;
  color: #222;
  letter-spacing: 0.125rem;
}
.main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 0.5rem;
  padding: 0.1rem;
}

.footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 1.5rem 1.5rem 1.5rem;
  box-sizing: border-box;
  gap: 1rem;
}
.footer.vertical {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.2rem;
  padding: 1rem 1.5rem 2.5rem 1.5rem;
}
.btn {
  flex: 1;
  margin: 0 0.5rem;
  padding: 0;
  border: none;
  background: none;
  background-position: center;
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

/* 按钮核心样式 */
.btn-img {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 0;
  border: none;
  width: 100%;
  height: var(--btn-height, 100px);
  min-height: var(--btn-min-height, 80px);
  max-width: var(--btn-max-width, 250px);
}
.btn.rule {
  background-image: url("/img/gzaimian-button-rule.png");
}
.btn.go {
  background-image: url("/img/gzaimian-button-go.png");
}

.btn:active {
  opacity: 0.8;
  transform: scale(0.96);
}

/* 小屏幕适配 */
@media (max-width: 320px) {
  :root {
    --btn-max-width: 200px;
    --btn-height: 60px;
    --btn-min-height: 50px;
  }
  .title {
    font-size: 1.8rem;
  }
  .footer {
    padding: 0.25rem 1.5rem 1.25rem 1.5rem;
  }
}

/* 大屏幕适配 */
@media (min-width: 430px) {
  :root {
    --btn-max-width: 300px;
    --btn-height: 120px;
    --btn-min-height: 100px;
  }
  .title {
    font-size: 2.5rem;
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
  box-sizing: border-box;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalFadeIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: center; /* 标题居中 */
  align-items: center;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #eee;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.modal-body {
  padding: 1.5rem;
  line-height: 1.6;
  color: #555;
  flex: 1; /* 让内容区自适应高度 */
}

.modal-body p {
  margin: 0 0 1rem 0;
}

.modal-body p:last-child {
  margin-bottom: 0;
}

/* 模态框底部按钮区域 */
.modal-footer {
  padding: 1rem 1.5rem 1.5rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: center;
}

/* 返回按钮样式 */
.back-btn {
  background-color: #ff2a86; /* Vue绿色作为示例，可根据设计调整 */
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.back-btn:hover {
  background-color: #ff2a86;
  transform: translateY(-2px);
}

.back-btn:active {
  transform: translateY(0);
  opacity: 0.9;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-content {
    max-height: 90vh;
    border-radius: 8px;
  }

  .modal-header {
    padding: 1rem 1rem 0.75rem 1rem;
  }

  .modal-header h2 {
    font-size: 1.25rem;
  }

  .modal-body {
    padding: 1rem;
  }
  
  .modal-footer {
    padding: 0.75rem 1rem 1rem;
    background-color: #ff2a86;
  }
  
.back-btn {
  padding: 0.35rem 1.0rem;
  font-size: 0.9rem;
  color: white; /* 白色字体 */
  border: 0.2rem solid white; /* 0.1rem白色边框 */
  border-radius: 1.2rem; /* 可选：添加圆角使按钮看起来更美观 */
  background-color: #ff2a86; /* 可选：透明背景 */
}
}
</style>
