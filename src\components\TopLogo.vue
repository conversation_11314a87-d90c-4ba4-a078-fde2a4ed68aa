<template>
  <div class="top-logo-container" :style="{ height }" ref="containerRef">
    <div 
      class="logo-slider"
      :class="{ animate: shouldAnimate, final: animationDone }"
      @animationend="handleAnimationEnd"
    >
      <!-- 第一张图片：动画后完全隐藏 -->
      <img
        class="logo-img first"
        :src="firstSrc"
        alt="初始Logo"
        @load="onFirstImageLoad"
        :style="{ display: animationDone ? 'none' : 'block' }"
      />
      <!-- 第二张图片：动画后单独显示 -->
      <img
        class="logo-img second"
        :src="secondSrc"
        alt="主要Logo"
        @load="onSecondImageLoad"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, onUnmounted } from "vue";

const props = defineProps({
  firstSrc: { type: String, required: true },
  secondSrc: { type: String, required: true },
  height: { type: String, default: "75px" },
  transitionDuration: { type: Number, default: 600 }
});

const firstImageLoaded = ref(false);
const secondImageLoaded = ref(false);
const animationTriggered = ref(false);
const shouldAnimate = ref(false);
const animationDone = ref(false);
const containerRef = ref(null);

function onFirstImageLoad() {
  firstImageLoaded.value = true;
}

function onSecondImageLoad() {
  secondImageLoaded.value = true;
}

// 动画结束后执行最终状态处理
function handleAnimationEnd() {
  shouldAnimate.value = false;
  animationDone.value = true;
  // 动画完成后调整容器布局，确保只显示第二张图
  adjustFinalLayout();
}

// 调整最终布局，彻底隐藏第一张图
function adjustFinalLayout() {
  if (containerRef.value) {
    const slider = containerRef.value.querySelector('.logo-slider');
    if (slider) {
      // 固定容器宽度为100vw，避免滚动
      slider.style.width = '100vw';
      // 确保第二张图完全占据容器
      const secondImg = slider.querySelector('.second');
      if (secondImg) {
        secondImg.style.width = '100%';
      }
    }
  }
}

watch([firstImageLoaded, secondImageLoaded], async () => {
  if (firstImageLoaded.value && secondImageLoaded.value && !animationTriggered.value) {
    animationTriggered.value = true;
    await nextTick();
    shouldAnimate.value = true; // 触发动画
  }
});

onMounted(() => {
  document.documentElement.style.setProperty('--logo-transition-duration', `${props.transitionDuration}ms`);
  updateContainerWidth();
  window.addEventListener('resize', updateContainerWidth);
});

function updateContainerWidth() {
  if (containerRef.value) {
    containerRef.value.style.width = `${window.innerWidth}px`;
  }
}

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerWidth);
});
</script>

<style scoped>
.top-logo-container {
  width: 100vw;
  height: 100%; /* 继承父容器高度（如85px） */
  overflow: hidden;
  margin: 0;
  padding: 0;
  line-height: 0; /* 消除文本基线影响 */
}

.logo-slider {
  display: flex;
  height: 100%;
  width: 200vw; /* 动画时需要容纳两张图 */
  transform: translateX(0);
  align-items: flex-start; /* 关键！强制顶部对齐 */
}

/* 动画执行中 */
.logo-slider.animate {
  animation: slideToSecond var(--logo-transition-duration) ease-out forwards;
}

/* 动画完成后的最终状态 */
.logo-slider.final {
  transform: translateX(0); /* 重置位移，因为已调整宽度 */
}

@keyframes slideToSecond {
  from { transform: translateX(0); }
  to { transform: translateX(-100vw); } /* 滑动到第二张图 */
}

.logo-img {
  width: 100vw; /* 保持动画一致性，两张图片宽度相同 */
  height: 100%; /* 高度填满容器 */
  object-fit: contain; /* 保持比例，不裁剪 */
  object-position: top; /* 关键！图片顶部对齐容器 */
  flex-shrink: 0;
  display: block;
}

/* 动画完成后第一张图彻底隐藏 */
.logo-img.first {
  transition: display 0s var(--logo-transition-duration);
}

</style>
