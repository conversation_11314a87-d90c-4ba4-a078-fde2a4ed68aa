import axios from 'axios';
import { Toast } from "antd-mobile";
import { useRouter } from 'vue-router'
const router = useRouter()

const http = axios.create({
    baseURL: "/api/",
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json;charset=UTF-8'
    }
});

// 请求拦截器
http.interceptors.request.use(
    config => {
        // 添加认证token
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器
http.interceptors.response.use(
    response => {
        // 处理业务逻辑错误
        if (response.data?.code !== 200) {
            Toast.show(response.data?.message || '业务错误');
            return Promise.reject(response.data);
        }
        return response.data;
    },
    error => {
        // 统一错误处理
        if (error.response) {
            switch (error.response.status) {
                case 401:
                    Toast.show('未授权，请重新登录');
                    router.push('/login');
                    break;
                case 403:
                    Toast.show('拒绝访问');
                    break;
                case 500:
                    Toast.show('服务器错误');
                    break;
                default:
                    Toast.show(`请求错误: ${error.message}`);
            }
        } else {
            Toast.show('网络错误，请检查连接');
        }
        return Promise.reject(error);
    }
);

export default http;