<!--余额-->
<script setup lang="ts">
import {onMounted} from "vue";
// @ts-ignore
import {transfer,wxConfig} from "@/http/balanceApi"
import { Toast } from "antd-mobile";
// import wx from "@/utils/jweixin-1.6.0"
declare global {
  interface Window {
    WeixinJSBridge: any;
  }
  interface Window {
    wx: any;
  }
}
const WeixinJSBridge = window.WeixinJSBridge;
const wx = window.wx;

onMounted(()=>{
  console.log("页面加载成功...")

})

// 余额提现
const balanceHandle = async ()=>{
  console.log("点击了提现按钮")
  let config = await wxConfig()
  console.log("config---",config)
  let res = await transfer()
  console.log("res---",res)
  if (res.code !== 200){
    Toast.show("请求失败,请稍后再试")
    return
  }
  console.log("res----",res.data)
  // console.log("wx---",wx)
  // console.log("<PERSON>xinJSBridge---",WeixinJSBridge)
  wx.config({
    // 参考：https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html
    ...config.data,
    beta: true,
    debug: false,
  });
  let data = res.data
  wx.ready(function () {
    console.log("wx.ready")
    wx.checkJsApi({
      jsApiList: ['requestMerchantTransfer'],
      success: function (res:any) {
        if (res.checkResult['requestMerchantTransfer']) {
          WeixinJSBridge.invoke('requestMerchantTransfer', {
                // mchId: '**********',
                // appId: 'wx721efc82a86c51ca',
                // package: 'ABBQO+oYAAABAAAAAADtKoYptnJFQFkTJgGcaBAAAADnGpepZahT9IkJjn90+1qgLyCQalHsxV4YUaOimgfFKRNV2KtVces35a8PZKlt568oeYvedLzna9iMKrFB2dYyCUSx/scB55IkjEZfyR6Gykb2460=',
                // ...res.data
                // mchId: data.mchId,
                // appId: data.appId,
                // package: data.package
                ...data
              },
              function (res:any) {
                if (res.err_msg === 'requestMerchantTransfer:ok') {
                  // res.err_msg将在页面展示成功后返回应用时返回success，并不代表付款成功
                  console.log("ok----")
                  Toast.show("支付成功---")
                }
              }
          );
        } else {
          alert('你的微信版本过低，请更新至最新版本。');
        }
      }
    });
  });
  console.log("wx.ready--end")
}
</script>

<template>
<div class="balance">
<div class="title" @click="balanceHandle()">余额提现</div>
</div>
</template>

<style scoped lang="scss">
.balance{
  width: 100%;
  height: 100%;
  .title{
    font-size: 30px;
    text-align: center;
  }
}
</style>